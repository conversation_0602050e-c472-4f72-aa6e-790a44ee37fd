"""
Configuration Manager - Handles configuration file management and settings persistence
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
try:
    from utils.singleton import Singleton
    from utils.common_utils import get_logger, safe_json_loads, safe_json_dumps, safe_file_read, safe_file_write
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.singleton import Singleton
    from utils.common_utils import get_logger, safe_json_loads, safe_json_dumps, safe_file_read, safe_file_write

logger = get_logger("config_manager")

class ConfigManager(Singleton):
    """Manages configuration files and settings for the AI Agent System"""
    
    def __init__(self):
        self.addon_dir = Path(__file__).parent.parent
        self.config_dir = self.addon_dir / "configs"
        self.config_file = self.config_dir / "settings.json"
        self.user_config_file = self.config_dir / "user_settings.json"
        
        # Default configuration
        self.default_config = {
            "system": {
                "log_level": "INFO",
                "max_agents": 5,
                "timeout_seconds": 30,
                "auto_save": True,
                "check_updates": True
            },
            "ai": {
                "default_provider": "openai",
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000
            },
            "crew": {
                "enabled": True,
                "max_concurrent_crews": 3,
                "default_execution_mode": "sequential"
            },
            "mcp": {
                "enabled": True,
                "auto_discover_servers": True,
                "connection_timeout": 10,
                "max_connections": 10
            },
            "ui": {
                "show_advanced_options": False,
                "auto_expand_panels": True,
                "theme": "default"
            }
        }
        
        self.user_config = {}
        self._ensure_config_dir()
        self._load_config()
    
    def _ensure_config_dir(self):
        """Ensure configuration directory exists"""
        self.config_dir.mkdir(exist_ok=True)
        logger.debug(f"Config directory: {self.config_dir}")
    
    def _load_config(self):
        """Load configuration from files"""
        # Load default config
        if not self.config_file.exists():
            self._save_default_config()
        
        # Load user config
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    self.user_config = json.load(f)
                logger.info("User configuration loaded")
            except Exception as e:
                logger.error(f"Failed to load user config: {e}")
                self.user_config = {}
        else:
            logger.info("No user configuration found, using defaults")
    
    def _save_default_config(self):
        """Save default configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, indent=2)
            logger.info("Default configuration saved")
        except Exception as e:
            logger.error(f"Failed to save default config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation (e.g., 'system.log_level')"""
        keys = key.split('.')
        
        # Try user config first
        value = self._get_nested_value(self.user_config, keys)
        if value is not None:
            return value
        
        # Fall back to default config
        value = self._get_nested_value(self.default_config, keys)
        if value is not None:
            return value
        
        return default
    
    def set(self, key: str, value: Any) -> bool:
        """Set configuration value with dot notation"""
        try:
            keys = key.split('.')
            self._set_nested_value(self.user_config, keys, value)
            self._save_user_config()
            logger.info(f"Configuration updated: {key} = {value}")
            return True
        except Exception as e:
            logger.error(f"Failed to set config {key}: {e}")
            return False
    
    def _get_nested_value(self, config: Dict, keys: list) -> Any:
        """Get nested value from configuration dictionary"""
        current = config
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        return current
    
    def _set_nested_value(self, config: Dict, keys: list, value: Any):
        """Set nested value in configuration dictionary"""
        current = config
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    def _save_user_config(self):
        """Save user configuration to file"""
        try:
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_config, f, indent=2)
            logger.debug("User configuration saved")
        except Exception as e:
            logger.error(f"Failed to save user config: {e}")
    
    def reset_to_defaults(self) -> bool:
        """Reset configuration to defaults"""
        try:
            self.user_config = {}
            if self.user_config_file.exists():
                self.user_config_file.unlink()
            logger.info("Configuration reset to defaults")
            return True
        except Exception as e:
            logger.error(f"Failed to reset config: {e}")
            return False
    
    def export_config(self, file_path: Path) -> bool:
        """Export current configuration to file"""
        try:
            merged_config = self._merge_configs()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(merged_config, f, indent=2)
            logger.info(f"Configuration exported to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
            return False
    
    def import_config(self, file_path: Path) -> bool:
        """Import configuration from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # Validate and merge with user config
            self.user_config.update(imported_config)
            self._save_user_config()
            logger.info(f"Configuration imported from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to import config: {e}")
            return False
    
    def _merge_configs(self) -> Dict:
        """Merge default and user configurations"""
        merged = self.default_config.copy()
        self._deep_update(merged, self.user_config)
        return merged
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """Deep update dictionary"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get_all_settings(self) -> Dict:
        """Get all current settings (merged)"""
        return self._merge_configs()
    
    def validate_config(self) -> tuple[bool, list]:
        """Validate current configuration"""
        errors = []
        
        # Basic validation
        try:
            max_agents = self.get('system.max_agents')
            if not isinstance(max_agents, int) or max_agents < 1 or max_agents > 50:
                errors.append("system.max_agents must be between 1 and 50")
            
            timeout = self.get('system.timeout_seconds')
            if not isinstance(timeout, int) or timeout < 5 or timeout > 600:
                errors.append("system.timeout_seconds must be between 5 and 600")
            
            temperature = self.get('ai.temperature')
            if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
                errors.append("ai.temperature must be between 0 and 2")
            
        except Exception as e:
            errors.append(f"Configuration validation error: {e}")
        
        return len(errors) == 0, errors

def get_config_manager():
    """Get the configuration manager instance"""
    return ConfigManager.get_instance()

def get_setting(key: str, default: Any = None) -> Any:
    """Get a configuration setting"""
    return get_config_manager().get(key, default)

def set_setting(key: str, value: Any) -> bool:
    """Set a configuration setting"""
    return get_config_manager().set(key, value)
