"""
MCP Client - Model Context Protocol client implementation for connecting to MCP servers
"""

import asyncio
import json
import subprocess
import time
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
try:
    from utils.singleton import Singleton
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.singleton import Singleton

# MCP SDK imports - Model Context Protocol Python SDK v1.11.0
# Successfully installed and configured
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    from mcp.client.streamable_http import streamablehttp_client
    from mcp.types import (
        Tool, Resource, Prompt,
        CallToolResult, ReadResourceResult, GetPromptResult,
        TextContent, ImageContent
    )
    MCP_AVAILABLE = True
except ImportError:
    # Fallback classes for when MCP is not installed
    class ClientSession:
        def __init__(self, *args, **kwargs):
            pass
        async def initialize(self):
            pass
        async def list_tools(self):
            return type('MockResult', (), {'tools': []})()
        async def call_tool(self, name, arguments):
            return type('MockResult', (), {'content': [], 'structuredContent': None})()
    
    class StdioServerParameters:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    def stdio_client(*args, **kwargs):
        return None
    
    def streamablehttp_client(*args, **kwargs):
        return None
    
    class Tool:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class Resource:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class Prompt:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class CallToolResult:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class ReadResourceResult:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class GetPromptResult:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class TextContent:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class ImageContent:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    MCP_AVAILABLE = False

try:
    from core.config_manager import get_config_manager
    from utils.logging_config import get_logger
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.config_manager import get_config_manager
    from utils.logging_config import get_logger

logger = get_logger("mcp_client")

class MCPIntegrationError(Exception):
    """Exception raised for MCP integration errors"""
    pass

class TransportType(Enum):
    """MCP transport types"""
    STDIO = "stdio"
    HTTP = "http"
    STREAMABLE_HTTP = "streamable_http"

class ConnectionStatus(Enum):
    """MCP connection status"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

@dataclass
class MCPServerConfig:
    """Configuration for an MCP server"""
    name: str
    transport: TransportType
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    timeout: int = 30
    auto_reconnect: bool = True
    max_retries: int = 3

@dataclass
class MCPConnection:
    """Represents an active MCP connection"""
    server_config: MCPServerConfig
    session: Optional[ClientSession] = None
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    last_error: Optional[str] = None
    connected_at: Optional[float] = None
    retry_count: int = 0

class MCPClient(Singleton):
    """MCP client for connecting to and managing MCP servers"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.connections: Dict[str, MCPConnection] = {}
        self.event_loop: Optional[asyncio.AbstractEventLoop] = None
        self.is_running = False
        
        # Callbacks
        self.on_server_connected: Optional[Callable[[str], None]] = None
        self.on_server_disconnected: Optional[Callable[[str, str], None]] = None
        self.on_tool_discovered: Optional[Callable[[str, Tool], None]] = None
        self.on_resource_discovered: Optional[Callable[[str, Resource], None]] = None
    
    def start(self):
        """Start the MCP client"""
        if not MCP_AVAILABLE:
            logger.warning("MCP SDK not available. Please install dependencies.")
            return False
        
        if self.is_running:
            logger.warning("MCP client already running")
            return True
        
        try:
            # Create event loop if not exists
            try:
                self.event_loop = asyncio.get_event_loop()
            except RuntimeError:
                self.event_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.event_loop)
            
            self.is_running = True
            logger.info("MCP client started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start MCP client: {e}")
            return False
    
    def stop(self):
        """Stop the MCP client"""
        if not self.is_running:
            return
        
        try:
            # Disconnect all servers
            for server_name in list(self.connections.keys()):
                asyncio.create_task(self.disconnect_server(server_name))
            
            self.is_running = False
            logger.info("MCP client stopped")
            
        except Exception as e:
            logger.error(f"Error stopping MCP client: {e}")
    
    async def connect_server(self, server_config: MCPServerConfig) -> bool:
        """Connect to an MCP server"""
        try:
            if not MCP_AVAILABLE:
                logger.error("MCP SDK not available")
                return False
            
            server_name = server_config.name
            logger.info(f"Connecting to MCP server: {server_name}")
            
            # Create connection object
            connection = MCPConnection(
                server_config=server_config,
                status=ConnectionStatus.CONNECTING
            )
            self.connections[server_name] = connection
            
            # Connect based on transport type
            if server_config.transport == TransportType.STDIO:
                success = await self._connect_stdio(connection)
            elif server_config.transport == TransportType.STREAMABLE_HTTP:
                success = await self._connect_http(connection)
            else:
                logger.error(f"Unsupported transport type: {server_config.transport}")
                return False
            
            if success:
                connection.status = ConnectionStatus.CONNECTED
                connection.connected_at = time.time()
                connection.retry_count = 0
                
                # Discover capabilities
                await self._discover_server_capabilities(connection)
                
                # Notify callback
                if self.on_server_connected:
                    self.on_server_connected(server_name)
                
                logger.info(f"Successfully connected to MCP server: {server_name}")
                return True
            else:
                connection.status = ConnectionStatus.ERROR
                return False
                
        except Exception as e:
            error_msg = f"Failed to connect to MCP server {server_config.name}: {e}"
            logger.error(error_msg)

            if server_config.name in self.connections:
                self.connections[server_config.name].status = ConnectionStatus.ERROR

            # Use standardized error handling
            try:
                from utils.error_handling import log_error
            except ImportError:
                # Fallback for when running as standalone module
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from utils.error_handling import log_error
            log_error(
                MCPIntegrationError(error_msg),
                context={"server_name": server_config.name, "transport": server_config.transport.value},
                suggestions=["Check server configuration", "Verify server is running", "Check network connectivity"]
            )
            self.connections[server_config.name].last_error = str(e)
            
            return False
    
    async def _connect_stdio(self, connection: MCPConnection) -> bool:
        """Connect using stdio transport"""
        try:
            config = connection.server_config
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=config.command,
                args=config.args or [],
                env=config.env or {}
            )
            
            # Connect to server
            transport = stdio_client(server_params)
            read_stream, write_stream = await transport.__aenter__()
            
            # Create session
            session = ClientSession(read_stream, write_stream)
            await session.initialize()
            
            connection.session = session
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect via stdio: {e}")
            return False
    
    async def _connect_http(self, connection: MCPConnection) -> bool:
        """Connect using HTTP transport"""
        try:
            config = connection.server_config
            
            if not config.url:
                raise ValueError("URL required for HTTP transport")
            
            # Connect to server
            transport = streamablehttp_client(config.url)
            read_stream, write_stream, _ = await transport.__aenter__()
            
            # Create session
            session = ClientSession(read_stream, write_stream)
            await session.initialize()
            
            connection.session = session
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect via HTTP: {e}")
            return False
    
    async def disconnect_server(self, server_name: str) -> bool:
        """Disconnect from an MCP server"""
        try:
            if server_name not in self.connections:
                logger.warning(f"Server not connected: {server_name}")
                return False
            
            connection = self.connections[server_name]
            
            # Close session if exists
            if connection.session:
                try:
                    # Note: Actual session cleanup would depend on MCP SDK implementation
                    connection.session = None
                except Exception as e:
                    logger.warning(f"Error closing session for {server_name}: {e}")
            
            # Update status
            connection.status = ConnectionStatus.DISCONNECTED
            connection.connected_at = None
            
            # Notify callback
            if self.on_server_disconnected:
                self.on_server_disconnected(server_name, "Manual disconnect")
            
            logger.info(f"Disconnected from MCP server: {server_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error disconnecting from server {server_name}: {e}")
            return False
    
    async def _discover_server_capabilities(self, connection: MCPConnection):
        """Discover server capabilities (tools, resources, prompts)"""
        try:
            session = connection.session
            server_name = connection.server_config.name
            
            if not session:
                return
            
            # Discover tools
            try:
                tools_result = await session.list_tools()
                for tool in tools_result.tools:
                    logger.debug(f"Discovered tool: {tool.name} on server {server_name}")
                    if self.on_tool_discovered:
                        self.on_tool_discovered(server_name, tool)
            except Exception as e:
                logger.warning(f"Failed to discover tools on {server_name}: {e}")
            
            # Discover resources
            try:
                resources_result = await session.list_resources()
                for resource in resources_result.resources:
                    logger.debug(f"Discovered resource: {resource.uri} on server {server_name}")
                    if self.on_resource_discovered:
                        self.on_resource_discovered(server_name, resource)
            except Exception as e:
                logger.warning(f"Failed to discover resources on {server_name}: {e}")
            
            # Discover prompts
            try:
                prompts_result = await session.list_prompts()
                for prompt in prompts_result.prompts:
                    logger.debug(f"Discovered prompt: {prompt.name} on server {server_name}")
            except Exception as e:
                logger.warning(f"Failed to discover prompts on {server_name}: {e}")
                
        except Exception as e:
            logger.error(f"Error discovering capabilities for {connection.server_config.name}: {e}")
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Optional[CallToolResult]:
        """Call a tool on an MCP server"""
        try:
            if server_name not in self.connections:
                logger.error(f"Server not connected: {server_name}")
                return None
            
            connection = self.connections[server_name]
            if not connection.session or connection.status != ConnectionStatus.CONNECTED:
                logger.error(f"Server not ready: {server_name}")
                return None
            
            logger.info(f"Calling tool {tool_name} on server {server_name}")
            result = await connection.session.call_tool(tool_name, arguments)
            
            logger.debug(f"Tool call result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on {server_name}: {e}")
            return None
    
    async def read_resource(self, server_name: str, resource_uri: str) -> Optional[ReadResourceResult]:
        """Read a resource from an MCP server"""
        try:
            if server_name not in self.connections:
                logger.error(f"Server not connected: {server_name}")
                return None
            
            connection = self.connections[server_name]
            if not connection.session or connection.status != ConnectionStatus.CONNECTED:
                logger.error(f"Server not ready: {server_name}")
                return None
            
            logger.info(f"Reading resource {resource_uri} from server {server_name}")
            result = await connection.session.read_resource(resource_uri)
            
            logger.debug(f"Resource read result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error reading resource {resource_uri} from {server_name}: {e}")
            return None
    
    def get_connection_status(self, server_name: str) -> Optional[ConnectionStatus]:
        """Get connection status for a server"""
        if server_name in self.connections:
            return self.connections[server_name].status
        return None
    
    def list_connected_servers(self) -> List[str]:
        """List all connected servers"""
        return [
            name for name, conn in self.connections.items()
            if conn.status == ConnectionStatus.CONNECTED
        ]
    
    def get_server_info(self, server_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a server"""
        if server_name not in self.connections:
            return None
        
        connection = self.connections[server_name]
        return {
            'name': server_name,
            'status': connection.status.value,
            'transport': connection.server_config.transport.value,
            'connected_at': connection.connected_at,
            'last_error': connection.last_error,
            'retry_count': connection.retry_count
        }

def get_mcp_client():
    """Get the MCP client instance"""
    return MCPClient.get_instance()

def register():
    """Register MCP client"""
    get_mcp_client().start()

def unregister():
    """Unregister MCP client"""
    get_mcp_client().stop()
