"""
Server Manager - MCP server discovery, configuration, and lifecycle management
"""

import json
import asyncio
import time
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
from dataclasses import dataclass, asdict

from .mcp_client import MCPClient, MCPServerConfig, TransportType, ConnectionStatus, get_mcp_client
try:
    from core.config_manager import get_config_manager
    from utils.logging_config import get_logger
    from utils.singleton import Singleton
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.config_manager import get_config_manager
    from utils.logging_config import get_logger
    from utils.singleton import Singleton

logger = get_logger("server_manager")

@dataclass
class ServerTemplate:
    """Template for creating MCP server configurations"""
    name: str
    description: str
    transport: TransportType
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    category: str = "general"
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class ServerManager(Singleton):
    """Manages MCP server configurations, discovery, and lifecycle"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.mcp_client = get_mcp_client()
        self.server_configs: Dict[str, MCPServerConfig] = {}
        self.server_templates: Dict[str, ServerTemplate] = {}
        self.health_check_interval = 30  # seconds
        self.health_check_task: Optional[asyncio.Task] = None
        self.is_monitoring = False
        
        # Callbacks
        self.on_server_added: Optional[Callable[[str], None]] = None
        self.on_server_removed: Optional[Callable[[str], None]] = None
        self.on_server_health_changed: Optional[Callable[[str, bool], None]] = None
        
        # Load configurations
        self._load_server_configs()
        self._load_server_templates()
    
    def _load_server_configs(self):
        """Load server configurations from config"""
        try:
            servers_config = self.config_manager.get('mcp.servers', {})
            
            for server_name, config_data in servers_config.items():
                try:
                    # Convert transport string to enum
                    transport_str = config_data.get('transport', 'stdio')
                    transport = TransportType(transport_str)
                    
                    server_config = MCPServerConfig(
                        name=server_name,
                        transport=transport,
                        command=config_data.get('command'),
                        args=config_data.get('args', []),
                        env=config_data.get('env', {}),
                        url=config_data.get('url'),
                        timeout=config_data.get('timeout', 30),
                        auto_reconnect=config_data.get('auto_reconnect', True),
                        max_retries=config_data.get('max_retries', 3)
                    )
                    
                    self.server_configs[server_name] = server_config
                    logger.debug(f"Loaded server config: {server_name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load server config {server_name}: {e}")
            
            logger.info(f"Loaded {len(self.server_configs)} server configurations")
            
        except Exception as e:
            logger.error(f"Failed to load server configurations: {e}")
    
    def _load_server_templates(self):
        """Load server templates"""
        # Built-in templates
        self.server_templates = {
            'filesystem': ServerTemplate(
                name='Filesystem Server',
                description='Access local filesystem resources',
                transport=TransportType.STDIO,
                command='mcp-server-filesystem',
                args=['--root', '.'],
                category='system',
                tags=['filesystem', 'files', 'local']
            ),
            'web_search': ServerTemplate(
                name='Web Search Server',
                description='Search the web for information',
                transport=TransportType.HTTP,
                url='https://api.example.com/mcp/search',
                category='web',
                tags=['search', 'web', 'information']
            ),
            'database': ServerTemplate(
                name='Database Server',
                description='Connect to databases',
                transport=TransportType.STDIO,
                command='mcp-server-database',
                args=['--connection-string', '${DB_URL}'],
                env={'DB_URL': 'sqlite:///data.db'},
                category='data',
                tags=['database', 'sql', 'data']
            ),
            'git': ServerTemplate(
                name='Git Server',
                description='Git repository operations',
                transport=TransportType.STDIO,
                command='mcp-server-git',
                category='development',
                tags=['git', 'version-control', 'development']
            ),
            'blender_tools': ServerTemplate(
                name='Blender Tools Server',
                description='Extended Blender operations',
                transport=TransportType.HTTP,
                url='http://localhost:8080/mcp',
                category='blender',
                tags=['blender', '3d', 'modeling', 'tools']
            )
        }
        
        # Load custom templates from file
        try:
            templates_dir = Path(__file__).parent.parent / "templates" / "mcp_servers"
            if templates_dir.exists():
                for template_file in templates_dir.glob("*.json"):
                    try:
                        with open(template_file, 'r', encoding='utf-8') as f:
                            template_data = json.load(f)
                        
                        template_name = template_file.stem
                        template = ServerTemplate(**template_data)
                        self.server_templates[template_name] = template
                        
                        logger.debug(f"Loaded server template: {template_name}")
                        
                    except Exception as e:
                        logger.error(f"Failed to load template {template_file}: {e}")
        
        except Exception as e:
            logger.error(f"Failed to load server templates: {e}")
        
        logger.info(f"Loaded {len(self.server_templates)} server templates")
    
    def add_server(self, server_config: MCPServerConfig, save: bool = True) -> bool:
        """Add a new server configuration"""
        try:
            server_name = server_config.name
            
            if server_name in self.server_configs:
                logger.warning(f"Server already exists: {server_name}")
                return False
            
            self.server_configs[server_name] = server_config
            
            if save:
                self._save_server_config(server_config)
            
            # Notify callback
            if self.on_server_added:
                self.on_server_added(server_name)
            
            logger.info(f"Added server configuration: {server_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add server {server_config.name}: {e}")
            return False
    
    def remove_server(self, server_name: str, disconnect: bool = True) -> bool:
        """Remove a server configuration"""
        try:
            if server_name not in self.server_configs:
                logger.warning(f"Server not found: {server_name}")
                return False
            
            # Disconnect if connected
            if disconnect:
                asyncio.create_task(self.mcp_client.disconnect_server(server_name))
            
            # Remove configuration
            del self.server_configs[server_name]
            
            # Remove from config
            self._remove_server_config(server_name)
            
            # Notify callback
            if self.on_server_removed:
                self.on_server_removed(server_name)
            
            logger.info(f"Removed server configuration: {server_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove server {server_name}: {e}")
            return False
    
    def create_server_from_template(self, template_name: str, server_name: str, 
                                  customizations: Optional[Dict[str, Any]] = None) -> Optional[MCPServerConfig]:
        """Create a server configuration from a template"""
        try:
            if template_name not in self.server_templates:
                logger.error(f"Template not found: {template_name}")
                return None
            
            template = self.server_templates[template_name]
            
            # Create server config from template
            server_config = MCPServerConfig(
                name=server_name,
                transport=template.transport,
                command=template.command,
                args=template.args.copy() if template.args else None,
                env=template.env.copy() if template.env else None,
                url=template.url
            )
            
            # Apply customizations
            if customizations:
                for key, value in customizations.items():
                    if hasattr(server_config, key):
                        setattr(server_config, key, value)
                    else:
                        logger.warning(f"Unknown customization key: {key}")
            
            logger.info(f"Created server config from template {template_name}: {server_name}")
            return server_config
            
        except Exception as e:
            logger.error(f"Failed to create server from template {template_name}: {e}")
            return None
    
    async def connect_server(self, server_name: str) -> bool:
        """Connect to a server"""
        try:
            if server_name not in self.server_configs:
                logger.error(f"Server configuration not found: {server_name}")
                return False
            
            server_config = self.server_configs[server_name]
            return await self.mcp_client.connect_server(server_config)
            
        except Exception as e:
            logger.error(f"Failed to connect to server {server_name}: {e}")
            return False
    
    async def disconnect_server(self, server_name: str) -> bool:
        """Disconnect from a server"""
        try:
            return await self.mcp_client.disconnect_server(server_name)
            
        except Exception as e:
            logger.error(f"Failed to disconnect from server {server_name}: {e}")
            return False
    
    async def connect_all_servers(self) -> Dict[str, bool]:
        """Connect to all configured servers"""
        results = {}
        
        for server_name in self.server_configs:
            try:
                success = await self.connect_server(server_name)
                results[server_name] = success
                
                if success:
                    logger.info(f"Connected to server: {server_name}")
                else:
                    logger.warning(f"Failed to connect to server: {server_name}")
                    
            except Exception as e:
                logger.error(f"Error connecting to server {server_name}: {e}")
                results[server_name] = False
        
        return results
    
    def start_health_monitoring(self):
        """Start health monitoring for connected servers"""
        if self.is_monitoring:
            logger.warning("Health monitoring already running")
            return
        
        self.is_monitoring = True
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info("Started server health monitoring")
    
    def stop_health_monitoring(self):
        """Stop health monitoring"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.health_check_task:
            self.health_check_task.cancel()
            self.health_check_task = None
        
        logger.info("Stopped server health monitoring")
    
    async def _health_check_loop(self):
        """Health check loop"""
        while self.is_monitoring:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry
    
    async def _perform_health_checks(self):
        """Perform health checks on all connected servers"""
        for server_name in self.server_configs:
            try:
                status = self.mcp_client.get_connection_status(server_name)
                is_healthy = status == ConnectionStatus.CONNECTED
                
                # TODO: Add more sophisticated health checks
                # - Ping server with simple request
                # - Check response time
                # - Verify capabilities still available
                
                if self.on_server_health_changed:
                    self.on_server_health_changed(server_name, is_healthy)
                    
            except Exception as e:
                logger.error(f"Health check failed for server {server_name}: {e}")
    
    def _save_server_config(self, server_config: MCPServerConfig):
        """Save server configuration to config"""
        try:
            servers_config = self.config_manager.get('mcp.servers', {})
            
            # Convert to dict for storage
            config_dict = {
                'transport': server_config.transport.value,
                'command': server_config.command,
                'args': server_config.args,
                'env': server_config.env,
                'url': server_config.url,
                'timeout': server_config.timeout,
                'auto_reconnect': server_config.auto_reconnect,
                'max_retries': server_config.max_retries
            }
            
            servers_config[server_config.name] = config_dict
            self.config_manager.set('mcp.servers', servers_config)
            
        except Exception as e:
            logger.error(f"Failed to save server config {server_config.name}: {e}")
    
    def _remove_server_config(self, server_name: str):
        """Remove server configuration from config"""
        try:
            servers_config = self.config_manager.get('mcp.servers', {})
            
            if server_name in servers_config:
                del servers_config[server_name]
                self.config_manager.set('mcp.servers', servers_config)
                
        except Exception as e:
            logger.error(f"Failed to remove server config {server_name}: {e}")
    
    def list_servers(self) -> List[str]:
        """List all configured servers"""
        return list(self.server_configs.keys())
    
    def list_templates(self) -> List[str]:
        """List all available templates"""
        return list(self.server_templates.keys())
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """Get server configuration"""
        return self.server_configs.get(server_name)
    
    def get_template(self, template_name: str) -> Optional[ServerTemplate]:
        """Get server template"""
        return self.server_templates.get(template_name)
    
    def get_servers_by_category(self, category: str) -> List[str]:
        """Get servers by category"""
        servers = []
        for template_name, template in self.server_templates.items():
            if template.category == category:
                servers.append(template_name)
        return servers
    
    def search_templates(self, query: str) -> List[str]:
        """Search templates by name, description, or tags"""
        query_lower = query.lower()
        results = []
        
        for template_name, template in self.server_templates.items():
            if (query_lower in template.name.lower() or
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template_name)
        
        return results

def get_server_manager():
    """Get the server manager instance"""
    return ServerManager.get_instance()

def register():
    """Register server manager"""
    get_server_manager().start_health_monitoring()

def unregister():
    """Unregister server manager"""
    get_server_manager().stop_health_monitoring()
